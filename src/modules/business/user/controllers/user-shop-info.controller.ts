import { Controller, Get, Post, Put, Delete, Body, UseGuards, Logger } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { JwtUserGuard } from '@modules/auth/guards';
import { CurrentUser } from '@modules/auth/decorators/current-user.decorator';
import { JwtPayload } from '@modules/auth/guards/jwt.util';
import { UserShopInfoService } from '../services/user-shop-info.service';
import { UserShopInfoDto, UserShopInfoResponseDto, UpdateUserShopInfoDto } from '../dto/user-shop-info.dto';

/**
 * Controller xử lý thông tin cửa hàng của người dùng
 */
@ApiTags('User Shop Info')
@Controller('v1/user/shop-info')
@UseGuards(JwtUserGuard)
@ApiBearerAuth()
export class UserShopInfoController {
  private readonly logger = new Logger(UserShopInfoController.name);

  constructor(
    private readonly userShopInfoService: UserShopInfoService,
  ) {}

  /**
   * Lấy thông tin cửa hàng của người dùng
   */
  @Get()
  @ApiOperation({
    summary: 'Lấy thông tin cửa hàng',
    description: 'Lấy thông tin cửa hàng của người dùng hiện tại'
  })
  @ApiResponse({
    status: 200,
    description: 'Thông tin cửa hàng',
    type: UserShopInfoResponseDto
  })
  @ApiResponse({
    status: 404,
    description: 'Chưa có thông tin cửa hàng'
  })
  async getShopInfo(@CurrentUser() user: JwtPayload) {
    try {
      this.logger.log(`User ${user.id} lấy thông tin shop`);

      const shopInfo = await this.userShopInfoService.getShopInfo(user.id);
      
      if (!shopInfo) {
        return {
          success: false,
          message: 'Chưa có thông tin cửa hàng. Vui lòng tạo mới.',
          data: null
        };
      }

      return {
        success: true,
        message: 'Lấy thông tin cửa hàng thành công',
        data: shopInfo
      };
    } catch (error) {
      this.logger.error(`Lỗi khi lấy thông tin shop cho user ${user.id}:`, error);
      throw error;
    }
  }

  /**
   * Tạo hoặc cập nhật thông tin cửa hàng
   */
  @Post()
  @ApiOperation({
    summary: 'Tạo hoặc cập nhật thông tin cửa hàng',
    description: 'Tạo mới hoặc cập nhật thông tin cửa hàng của người dùng'
  })
  @ApiResponse({
    status: 200,
    description: 'Thông tin cửa hàng đã được lưu',
    type: UserShopInfoResponseDto
  })
  @ApiResponse({
    status: 400,
    description: 'Dữ liệu không hợp lệ'
  })
  async upsertShopInfo(
    @Body() shopInfoDto: UserShopInfoDto,
    @CurrentUser() user: JwtPayload
  ) {
    try {
      this.logger.log(`User ${user.id} upsert thông tin shop`);

      const shopInfo = await this.userShopInfoService.upsertShopInfo(user.id, shopInfoDto);
      
      return {
        success: true,
        message: 'Lưu thông tin cửa hàng thành công',
        data: shopInfo
      };
    } catch (error) {
      this.logger.error(`Lỗi khi upsert thông tin shop cho user ${user.id}:`, error);
      throw error;
    }
  }

  /**
   * Cập nhật thông tin cửa hàng
   */
  @Put()
  @ApiOperation({
    summary: 'Cập nhật thông tin cửa hàng',
    description: 'Cập nhật một phần thông tin cửa hàng của người dùng'
  })
  @ApiResponse({
    status: 200,
    description: 'Thông tin cửa hàng đã được cập nhật',
    type: UserShopInfoResponseDto
  })
  @ApiResponse({
    status: 404,
    description: 'Không tìm thấy thông tin cửa hàng'
  })
  @ApiResponse({
    status: 400,
    description: 'Dữ liệu không hợp lệ'
  })
  async updateShopInfo(
    @Body() updateDto: UpdateUserShopInfoDto,
    @CurrentUser() user: JwtPayload
  ) {
    try {
      this.logger.log(`User ${user.id} cập nhật thông tin shop`);

      const shopInfo = await this.userShopInfoService.updateShopInfo(user.id, updateDto);
      
      return {
        success: true,
        message: 'Cập nhật thông tin cửa hàng thành công',
        data: shopInfo
      };
    } catch (error) {
      this.logger.error(`Lỗi khi cập nhật thông tin shop cho user ${user.id}:`, error);
      throw error;
    }
  }

  /**
   * Xóa thông tin cửa hàng
   */
  @Delete()
  @ApiOperation({
    summary: 'Xóa thông tin cửa hàng',
    description: 'Xóa thông tin cửa hàng của người dùng'
  })
  @ApiResponse({
    status: 200,
    description: 'Thông tin cửa hàng đã được xóa'
  })
  @ApiResponse({
    status: 404,
    description: 'Không tìm thấy thông tin cửa hàng'
  })
  async deleteShopInfo(@CurrentUser() user: JwtPayload) {
    try {
      this.logger.log(`User ${user.id} xóa thông tin shop`);

      await this.userShopInfoService.deleteShopInfo(user.id);
      
      return {
        success: true,
        message: 'Xóa thông tin cửa hàng thành công'
      };
    } catch (error) {
      this.logger.error(`Lỗi khi xóa thông tin shop cho user ${user.id}:`, error);
      throw error;
    }
  }

  /**
   * Kiểm tra thông tin cửa hàng có tồn tại không
   */
  @Get('exists')
  @ApiOperation({
    summary: 'Kiểm tra thông tin cửa hàng',
    description: 'Kiểm tra xem người dùng đã có thông tin cửa hàng chưa'
  })
  @ApiResponse({
    status: 200,
    description: 'Kết quả kiểm tra',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: true },
        message: { type: 'string', example: 'Kiểm tra thành công' },
        data: {
          type: 'object',
          properties: {
            exists: { type: 'boolean', example: true },
            hasCompleteInfo: { type: 'boolean', example: true }
          }
        }
      }
    }
  })
  async checkShopInfoExists(@CurrentUser() user: JwtPayload) {
    try {
      this.logger.log(`User ${user.id} kiểm tra thông tin shop`);

      const exists = await this.userShopInfoService.hasShopInfo(user.id);
      const shopInfo = exists ? await this.userShopInfoService.getShopInfo(user.id) : null;
      
      // Kiểm tra thông tin có đầy đủ không
      const hasCompleteInfo = shopInfo && 
        shopInfo.shopName && 
        shopInfo.shopPhone && 
        shopInfo.shopAddress && 
        shopInfo.shopProvince && 
        shopInfo.shopDistrict;
      
      return {
        success: true,
        message: 'Kiểm tra thành công',
        data: {
          exists,
          hasCompleteInfo: !!hasCompleteInfo
        }
      };
    } catch (error) {
      this.logger.error(`Lỗi khi kiểm tra thông tin shop cho user ${user.id}:`, error);
      throw error;
    }
  }
}
